import { MenuItem } from "../components/header/menu";
import { ServiceMenutype } from "../models/common.model";

export const aboutDropDownMenuItem: ServiceMenutype[] = [
  { label: "Our Team", path: "/team-veterinary" },
  { label: "Photo Gallery", path: "/photo-gallery" },
  {
    label: "FAQS",
    path: "/faqs",
  },
];

export const resoucesDropDownMenuItem: ServiceMenutype[] = [
  {
    label: "Blog",
    path: "/blog",
  },
  { label: "Online Forms", path: "" },
  {
    label: "Payment Options",
    path: "",
  },
  {
    label: "Helpfull Links",
    path: "",
  },
];

export const menuItems: MenuItem[] = [
  {
    label: "Home",
    path: "/",
  },
  {
    label: "About Us",
    path: "/about-dry-creek-veterinary-hospital",
    children: [
      { label: "Our Team", path: "/team-veterinary" },
      { label: "Photo Gallery", path: "/photo-gallery" },
      {
        label: "FAQS",
        path: "/faqs",
      },
    ],
  },
  {
    label: "Services",
    path: "/services",
  },
  {
    label: "Resources",
    children: [
      {
        label: "Blog",
        path: "",
      },
      {
        label: "Online Forms",
        path: "",
        children: [
          {
            label: "New Client Form",
            path: "/online-forms/new-client-form",
          },
          {
            label: "Consent Form",
            path: "/online-forms/consent-form",
          },
        ],
      },
      {
        label: "Payment Options",
        path: "/payment-option",
      },
      {
        label: "Helpfull Links",
        path: "/helpful-links",
      },
    ],
  },
  {
    label: "Online Pharmacy",
    path: "https://drycreekveterinaryhospital.securevetsource.com/site/view/site/view/HomeDelivery.pml?retUrl=https://www.drycreekvet.com&cms=",
    hasRedirectUrl: true,
  },
  {
    label: "Contact",
    path: "/contact-us",
  },
  {
    label: "Appointment",
    path: "/contact-us",
    isButton: true,
  },
];

export const emailValidationRule = {
  pattern: {
    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: "Invalid email format",
  },
};

export const mobileValidationRule = {
  pattern: {
    value: /^[0-9]{10}$/, // matches exactly 10 digits
    message: "Enter valid mobile number",
  },
};

export const zipCodeValidationRule = {
  required: "Required",
  pattern: {
    value: /^\d{5}(-\d{4})?$/,
    message: "Valid ZIP (e.g. 12345 or 12345-6789)",
  },
};
