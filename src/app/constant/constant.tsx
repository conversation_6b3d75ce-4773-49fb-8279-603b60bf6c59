import { MenuItem } from "../components/header/menu";
import { ServiceMenutype } from "../models/common.model";

export const aboutDropDownMenuItem: ServiceMenutype[] = [
  { label: "Our Team", path: "/team-veterinary" },
  { label: "Photo Gallery", path: "/photo-gallery" },
  {
    label: "FAQS",
    path: "/faqs",
  },
];

export const resoucesDropDownMenuItem: ServiceMenutype[] = [
  {
    label: "Blog",
    path: "/blog",
  },
  { label: "Online Forms", path: "" },
  {
    label: "Payment Options",
    path: "",
  },
  {
    label: "Helpfull Links",
    path: "",
  },
];

export const menuItems: MenuItem[] = [
  {
    label: "Home",
    path: "/",
  },
  {
    label: "About Us",
    path: "/about-dry-creek-veterinary-hospital",
    children: [
      { label: "Our Team", path: "/team-veterinary" },
      { label: "Photo Gallery", path: "/photo-gallery" },
      {
        label: "FAQS",
        path: "/faqs",
      },
    ],
  },
  {
    label: "Services",
    path: "/services",
  },
  {
    label: "Resources",
    children: [
      {
        label: "Blog",
        path: "",
      },
      {
        label: "Online Forms",
        path: "",
        children: [
          {
            label: "New Client Form",
            path: "/online-forms/new-client-form",
          },
          {
            label: "Consent Form",
            path: "/online-forms/consent-form",
          },
        ],
      },
      {
        label: "Payment Options",
        path: "",
      },
      {
        label: "Helpfull Links",
        path: "",
      },
    ],
  },
  {
    label: "Online Pharmacy",
    path: "",
  },
  {
    label: "Contact",
    path: "/contact-us",
  },
  {
    label: "Appointment",
    path: "/appointment",
    isButton: true,
  },
];
