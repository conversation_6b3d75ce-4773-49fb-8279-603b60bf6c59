"use client";
import {
  aboutDropDownMenuItem,
  menuItems,
  resoucesDropDownMenuItem,
} from "@/app/constant/constant";
import { ChevronDown, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useRef, useState } from "react";
import veternaryLogo from "../../../../public/logos/Dry-creek-veterinary-hospital-logo.webp";
import NavMenu from "./menu";

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [activeMenu, setActiveMenu] = useState<string | null>(null);

  const router = useRouter();
  const pathName = usePathname();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuClick = (menu: string) => {
    setActiveMenu(activeMenu === menu ? null : menu);
  };

  const isActive = (path: string) => pathName === path;

  return (
    <header className="bg-white fixed z-50 w-full">
      {/* Mobile Header */}
      <div className="md:hidden w-full flex items-center justify-between">
        <div className="py-2">
          <Image
            src={veternaryLogo}
            alt="Veterinary Medical Center Logo"
            loading="eager"
            className="cursor-pointer w-20 p-1 rounded-md"
            onClick={() => router?.push("/")}
          />
        </div>
        <button
          onClick={toggleMobileMenu}
          className="flex items-center px-4 py-2"
          aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
        >
          {isMobileMenuOpen ? (
            <svg
              className="w-8 h-8"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          ) : (
            <svg
              className="w-8 h-8"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16m-7 6h7"
              />
            </svg>
          )}
        </button>
      </div>

      {/* Desktop Navigation */}
      <div className="shadow-sm">
        <nav className="lg:container lg:mx-auto hidden md:flex h-20 w-full items-center justify-between bg-white px-2">
          <Image
            src={veternaryLogo}
            alt="Veterinary Medical Center Logo"
            loading="eager"
            width={600}
            height={762}
            className="h-20 w-auto bg-white p-1 rounded py-1"
            onClick={() => router?.push("/")}
          />

          <NavMenu items={menuItems} />
        </nav>
      </div>

      {/* Mobile Phone Info */}
      {/* <div className="flex md:hidden">
        <div className="text-sm tracking-wide flex items-center pb-2 px-4">
          <p className="flex items-center font-semibold">
            <Phone size={16} className="mr-1" />
            <a href="tel:**********" className="hover:text-sky-300">(*************</a>
          </p>
        </div>
      </div> */}

      {/* Mobile Dropdown Menu */}
      <div
        className={`md:hidden ${
          isMobileMenuOpen ? "max-h-[calc(100vh-90px)]" : "max-h-0"
        } overflow-y-auto transition-all duration-300 ease-in-out bg-blue-600`}
      >
        <nav className="flex flex-col space-y-2 pt-4 px-4 pb-6">
          <Link
            href="/"
            className={` mb-2 py-2 px-2 text-white rounded-sm ${
              isActive("/") ? "!text-yellow-300" : ""
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Home
          </Link>

          {/* About Mobile */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => {
                router?.push("/about-dry-creek-veterinary-hospital");
                setIsMobileMenuOpen(false);
              }}
              className={` py-2 flex cursor-pointer text-white items-center w-full px-2 ${
                isActive("/aboutus/") || pathName?.includes("/aboutus/")
                  ? "text-blue-600"
                  : ""
              }`}
            >
              About Us
              {activeMenu === "aboutus" ? (
                <ChevronDown
                  className="ml-2"
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("aboutus");
                  }}
                />
              ) : (
                <ChevronRight
                  className="ml-2"
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("aboutus");
                  }}
                />
              )}
            </button>
            {activeMenu === "aboutus" && (
              <ul className="flex flex-col space-y-3 py-2  text-sm">
                {aboutDropDownMenuItem?.map((item, index) => (
                  <li key={index} className="flex">
                    <Link
                      href={item?.path || "#"}
                      className={`flex-1 py-2 px-4 text-white rounded-sm ${
                        isActive(`${item?.path}/`) ? "text-blue-600" : ""
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            )}
          </div>

          <Link
            href="/services"
            className={`mb-2 py-2 px-2 text-white rounded-sm ${
              isActive("/contact/") ? "text-blue-600" : ""
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Services
          </Link>

          {/* Resources Mobile */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => {
                router?.push("/");
                setIsMobileMenuOpen(false);
              }}
              className={`py-2 flex items-center w-full text-white px-2 ${
                [
                  "/resources/",
                  "/veterinary-lab-sacramento/",
                  "/veterinary-hospital-gallery/",
                  "/faqs/",
                ].includes(pathName)
                  ? " text-blue-600"
                  : ""
              }`}
            >
              Resources
              {activeMenu === "resources" ? (
                <ChevronDown
                  className="ml-2"
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("resources");
                  }}
                />
              ) : (
                <ChevronRight
                  className="ml-2"
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("resources");
                  }}
                />
              )}
            </button>
            {activeMenu === "resources" && (
              <ul className="flex flex-col space-y-3 py-2  text-sm">
                {resoucesDropDownMenuItem?.map((item, index) => (
                  <li key={index} className="flex">
                    <Link
                      href={item?.path || "#"}
                      className={`flex-1 py-2 px-4 text-white rounded-sm ${
                        isActive(`${item?.path}/`) ? "text-blue-600" : ""
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {item?.label}
                    </Link>
                  </li>
                ))}
              </ul>
            )}
          </div>

          <Link
            href="https://drycreekveterinaryhospital.securevetsource.com/site/view/site/view/HomeDelivery.pml?retUrl=https://www.drycreekvet.com&cms="
            className={`mb-2 py-2 px-2 text-white rounded-sm ${
              isActive("/contact/") ? "text-blue-600" : ""
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Online Pharmacy
          </Link>

          <Link
            href="/contact-us"
            className={`mb-2 py-2 px-2 text-white rounded-sm ${
              isActive("/contact-us/") ? "text-blue-600" : ""
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Contact Us
          </Link>

          <Link
            href="/contact-us"
            className={`mb-2 py-2 px-2 rounded-lg bg-white`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            APPOINTMENT
          </Link>
        </nav>
      </div>
    </header>
  );
}
