"use client";

import { ChevronRight } from "lucide-react";
import Image from "next/image";

export default function ServiceSection() {
  const services = [
    {
      title: "Walk-ins Welcome",
      description:
        "When a health issue arises unexpectedly in your pet, count on Dry Creek Veterinary Hospital in Galt, CA, to be there to help. We welcome you to make a walk-in visit with us so that our skilled veterinary team can address your pet’s issue right away.",
      image: "../images/veterinary-clinic-cartoon-image.webp",
    },
    {
      title: "Pet Dental Care",
      description:
        "Our specialized pet dental services at Dry Creek Veterinary Hospital in Galt, CA, are key to keeping your pet’s teeth healthy and pain-free. Schedule a cleaning from our dedicated team and help ensure your pets enjoy a brighter smile and a healthier life.",
      image: "../images/a-person-checking-a-dog-teeth.webp",
    },
    {
      title: "Pet Emergencies",
      description:
        "Emergencies can happen to pets at any time, which is why Dry Creek Veterinary Hospital is here to address any and all of your urgent pet care needs in Galt, CA. Our team is trained to handle a wide range of emergencies to ensure your pet receives the attention they need as quickly as possible.",
      image: "../images/routine-check-puppy-s-health.avif",
    },
    {
      title: "Pet Soft Tissue Surgeries",
      description:
        "When your pet is suffering from internal discomfort or other underlying symptoms, soft tissue surgery can provide a lasting cure. Dry Creek Veterinary Hospital conducts pet soft tissue surgery in Galt, CA, with precision and the utmost care for your pet’s safety, working to correct their condition quickly and comprehensively.",
      image: "../images/medium-shot-people-with-cute-cat.avif",
    },
  ];

  return (
    <section id="services" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">
            Our Veterinary Services in Galt, CA
          </h2>
          <p className=" text-gray-600 max-w-3xl mx-auto">
            Dry Creek Veterinary Hospital offers a full range of exceptional
            veterinary services designed to ensure your pets remain healthy and
            joyful.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
          {services.map((service, index) => (
            <div
              key={index}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-gray-100"
            >
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={service.image}
                  alt={service.title}
                  width={400}
                  height={192}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {service.description}
                </p>
                <a href="/contact-us">
                  <button className="flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors cursor-pointer">
                    Book Now
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </button>
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
