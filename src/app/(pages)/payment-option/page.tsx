import React from "react";
import { Calendar, CreditCard, DollarSign, Zap } from "lucide-react";
import Image from "next/image";

export default function PaymentOption() {
  return (
    <div className="min-h-screen bg-gray-50">
      <section id="about" className="py-20 bg-gray-50">
        <div className="container mx-auto relative overflow-hidden px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
                  Payment <span className="text-blue-600">Options</span>
                </h2>
                <p className="text-gray-600 max-w-2xl">
                  Learn more about the payment options we accept below.
                </p>
                <a href="/contact-us">
                  <button className="bg-blue-600 text-white px-8 py-4 mt-8 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center cursor-pointer">
                    <Calendar className="w-5 h-5 mr-2" />
                    Book Appointment
                  </button>
                </a>
              </div>
            </div>

            <div className="relative flex md:justify-end">
              <Image
                src="/images/payment-option.avif"
                alt="Veterinary hospital interior"
                width={500}
                height={400}
                className="rounded-2xl z-10 shadow-xl  rounded-tl-[20px] rounded-tr-[20px] rounded-br-[20px] rounded-bl-[50px] md:rounded-bl-[100px]"
              />
            </div>
          </div>
          <div className="absolute top-10 left-10 w-16 h-16 bg-blue-200/30 rounded-full "></div>
          <div className="absolute bottom-20 left-[55rem] w-32 h-32 bg-blue-300/30 rounded-full"></div>
        </div>
      </section>
      <section className="bg-blue-600 py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 place-items-center">
            {/* Credit Cards */}
            <div className="flex flex-col items-center group cursor-pointer">
              <div className="p-5 bg-green-500 hover:bg-green-600 rounded-lg flex items-center justify-center mb-2 transition-colors duration-200">
                <CreditCard className="w-16 h-16 text-white" />
              </div>
              <span className="text-lg font-medium text-white tracking-wider group-hover:text-green-600 transition-colors duration-200">
                CREDIT CARDS
              </span>
            </div>

            {/* Cash */}
            <div className="flex flex-col items-center group cursor-pointer">
              <div className="p-5 bg-green-500 hover:bg-green-600 rounded-lg flex items-center justify-center mb-2 transition-colors duration-200">
                <DollarSign className="w-16 h-16 text-white" />
              </div>
              <span className="text-lg font-medium text-white tracking-wider group-hover:text-green-600 transition-colors duration-200">
                CASH
              </span>
            </div>

            {/* CareCredit */}
            <div className="flex flex-col items-center group cursor-pointer">
              <div className="p-5 bg-green-500 hover:bg-green-600 rounded-lg flex items-center justify-center mb-2 transition-colors duration-200">
                <Zap className="w-16 h-16 text-white" />
              </div>
              <span className="text-lg font-medium text-white tracking-wider group-hover:text-green-600 transition-colors duration-200">
                CARECREDIT
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Main content */}
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 sm:grid-cols-2  gap-8 place-items-center">
            {/* Left side - Logo */}
            <div className="flex-1">
              <Image
                src="/images/carecredit-logo.jpeg"
                alt="CareCredit Logo"
                width={500}
                height={200}
              />
            </div>

            {/* Right side - Content */}
            <div className="flex-1 ml-12">
              <div className="space-y-6">
                <h2 className="text-2xl sm:text-4xl font-bold text-blue-600 mb-4">
                  CareCredit
                </h2>

                <p className="text-gray-700 mb-6 leading-relaxed">
                  Whether it&apos;s a routine checkup or emergency surgery, you
                  shouldn&apos;t have to worry about how to get the best medical
                  care for your pet. That&apos;s why we&#39;re pleased to accept
                  the CareCredit healthcare credit card, North America&apos;s
                  leading healthcare financing program. CareCredit lets you say
                  &quot;yes&quot; to the best treatment for your pet
                  immediately, and pay for it over time with monthly payments
                  that fit easily into your budget.
                </p>

                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Highlights of the CareCredit Program
                </h3>

                <ul className="space-y-2 mb-8">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span className="text-gray-700">
                      Extended Payment Plans
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span className="text-gray-700">
                      Interest-Free For 6 Months
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span className="text-gray-700">Quick & Easy Approval</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span className="text-gray-700">
                      Immediate Access to Funds
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span className="text-gray-700">No Annual Fee</span>
                  </li>
                </ul>

                <button
                  className="bg-blue-600 hover:bg-blue-700 cursor-pointer text-white font-bold py-3 px-8 rounded-full transition-colors duration-200"
                  onClick={(e) => {
                    e.preventDefault();
                    window.open("https://www.carecredit.com/", "_blank");
                  }}
                >
                  CARECREDIT
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
