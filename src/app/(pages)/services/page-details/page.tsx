"use client";

import { Calendar, CheckCircle } from "lucide-react";
import Image from "next/image";

export default function PageDetails() {
  const services = [
    "Pet Aftercare",
    "Pet Dental Care",
    "Pet Emergencies",
    "Pet Laboratory",
    "Pet Medical Facilities",
    "Pet Radiology",
    "Pet Soft Tissue Surgeries",
    "Pet Surgery",
    "Pet Walk-Ins",
  ];
  return (
    <div>
      <section id="about" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
                  Pet <span className="text-blue-600">Aftercare</span>
                </h2>
                <p className=" text-gray-600 max-w-lg">
                  The bond you share with your pets is irreplaceable, even after
                  they’ve passed on. At Dry Creek Veterinary Hospital, we hope
                  to support you through your loss with dedicated aftercare
                  services to help you process your grief.
                </p>
                <button className="bg-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Book Appointment
                </button>
              </div>
            </div>

            <div className="relative flex justify-end">
              <Image
                src="/images/pet-aftercare-1.webp"
                alt="Veterinary hospital interior"
                width={600}
                height={400}
                className="rounded-2xl shadow-xl md:h-[380px] md:w-[600px] rounded-tl-[20px] rounded-tr-[20px] rounded-br-[20px] rounded-bl-[50px] md:rounded-bl-[100px]"
              />
            </div>
          </div>
        </div>
      </section>

      <section
        id="team"
        className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100"
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="space-y-4 pb-7">
                <h2 className="md:text-2xl font-bold text-gray-900">
                  What to Expect from Our Pet Aftercare Services
                </h2>
                <p className=" text-gray-600 leading-relaxed">
                  We aim to provide compassionate and professional care to help
                  ease the emotional and logistical challenges of pet loss. Our
                  services are designed to meet your needs and honor your pet’s
                  memory.
                </p>
                <p className=" text-gray-600 leading-relaxed">
                  Here’s what we offer:
                </p>
                <ul className="space-y-3 text-gray-700 pl-5">
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <span>
                      Private Cremation: For those who prefer cremation, we
                      offer private options to return your pet’s ashes in a
                      keepsake urn.
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <span>
                      Communal Cremation: This option is available for pet
                      owners who do not wish to keep their pet’s ashes.
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <span>
                      Memorial Options: We provide a range of keepsakes,
                      including paw prints and engraved plaques, to remember
                      your pet.
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <span>
                      Grief Support: Our team offers guidance and resources to
                      help you cope with your loss.
                    </span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="relative flex justify-end">
              <Image
                src="/images/pet-aftercare.webp"
                alt="Veterinary team"
                width={600}
                height={400}
                className="rounded-2xl shadow-xl"
              />
            </div>

            <div className="space-y-4 pb-7 col-span-2">
              <h2 className="md:text-2xl font-bold text-gray-900">
                Why Choose Dry Creek Veterinary Hospital for Pet Aftercare?
              </h2>
              <p className=" text-gray-600 leading-relaxed">
                Dry Creek Veterinary Hospital seeks to support you with
                dedication and empathy to help you find closure from your loss.
                When you choose our pet aftercare in Galt, CA, you can expect
                the following:
              </p>
              <ul className="space-y-3 text-gray-700 pl-5">
                <li className="flex items-start">
                  <span className="text-blue-600 mr-3 mt-1">•</span>
                  <span>
                    Experienced and Compassionate Staff: Our team has years of
                    experience providing end-of-life care, ensuring that every
                    pet is treated with the utmost care and respect.
                  </span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-3 mt-1">•</span>
                  <span>
                    Personalized Care: We work closely with you to meet your
                    individual needs and preferences and ensure that your pet
                    receives the appropriate care.
                  </span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-3 mt-1">•</span>
                  <span>
                    Support Through the Process: We’re here to help guide you
                    through decisions and provide options that fit your
                    emotional and financial needs.
                  </span>
                </li>
              </ul>
            </div>
            <div className="space-y-4 pb-7 col-span-2">
              <h2 className="md:text-2xl font-bold text-gray-900">
                Contact Us for Pet Aftercare in Galt, CA
              </h2>
              <p className=" text-gray-600 leading-relaxed">
                Dry Creek Veterinary Hospital is committed to offering
                supportive, compassionate pet aftercare in Galt, CA. We aim to
                make this experience as smooth and respectful as possible for
                you and your pet. If you have any questions or need further
                assistance, please contact our team. We’re here to help.
              </p>
            </div>
          </div>
        </div>
      </section>
      <section
        id="services"
        className="py-20 bg-gradient-to-br from-gray-50 to-blue-50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">
              Veterinary Services in Galt, CA
            </h2>
            <div className="w-24 h-1 bg-blue-600 mx-auto mb-6"></div>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service, index) => (
              <>
                <a href="/services/page-details">
                  <div
                    key={index}
                    className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow group"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="bg-blue-100 group-hover:bg-blue-600 transition-colors rounded-full p-3">
                        <CheckCircle className="h-6 w-6 text-blue-600 group-hover:text-white" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                        {service}
                      </h3>
                    </div>
                  </div>
                </a>
              </>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
