"use client";

import React from "react";
import { Calendar } from "lucide-react";
import Image from "next/image";

type ImageType = {
  id: number;
  src: string;
  alt: string;
  title: string;
};

const images: ImageType[] = [
  {
    id: 1,
    src: "/images/pet-aftercare.webp",
    alt: "Pet Aftercare",
    title: "Pet Aftercare",
  },
  {
    id: 2,
    src: "/images/pet-dental-care.webp",
    alt: "Pet Dental Care",
    title: "Pet Dental Care",
  },
  {
    id: 3,
    src: "/images/pet-emergencies.webp",
    alt: "Pet Emergencies",
    title: "Pet Emergencies",
  },
  {
    id: 4,
    src: "/images/pet-laboratory.webp",
    alt: "Pet Laboratory",
    title: "Pet Laboratory",
  },
  {
    id: 5,
    src: "/images/pet-medical-facilities.avif",
    alt: "Pet Medical Facilities",
    title: "Pet Medical Facilities",
  },
  {
    id: 6,
    src: "/images/pet-radiology.webp",
    alt: "Pet Radiology",
    title: "Pet Radiology",
  },
  {
    id: 7,
    src: "/images/pet-soft-tissue-surgeries.webp",
    alt: "Pet Soft Tissue Surgeries",
    title: "Pet Soft Tissue Surgeries",
  },
  {
    id: 8,
    src: "/images/pet-surgery.webp",
    alt: "Pet Surgery",
    title: "Pet Surgery",
  },
  {
    id: 9,
    src: "/images/pet-walk-ins.webp",
    alt: "Pet Walk-Ins",
    title: "Pet Walk-Ins",
  },
];

export default function PhotoGallery() {
  return (
    <div>
      <p className="text-center py-3 bg-black text-white mt-[22px] md:mt-[0px]">
        Due to limited staffing, we are now accepting emergencies only on a
        priority basis on Saturdays.
      </p>
      <section id="about" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
                  Veterinary <span className="text-blue-600">Services</span>
                </h2>
                <p className="text-gray-600 max-w-2xl">
                  At Dry Creek Veterinary Hospital, you can trust that your pets
                  are in great hands. Our compassionate, skillful veterinary
                  staff are here to help when you need us most. We look forward
                  to seeing you soon.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <a href="/contact-us">
                  <button className="bg-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center cursor-pointer">
                    <Calendar className="w-5 h-5 mr-2" />
                    Book Appointment
                  </button>
                </a>
              </div>
            </div>

            <div className="relative flex justify-end">
              <Image
                src="/images/veterinary-services.webp"
                alt="Veterinary hospital interior"
                width={600}
                height={400}
                className="rounded-2xl shadow-xl md:h-[380px] md:w-[600px] rounded-tl-[20px] rounded-tr-[20px] rounded-br-[20px] rounded-bl-[50px] md:rounded-bl-[100px]"
              />
            </div>
          </div>
        </div>
      </section>

      <section id="team" className="py-8 bg-blue-600">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-1 gap-12 items-center text-center">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-white">
                Veterinary Services in Galt, CA
              </h2>
            </div>
          </div>
        </div>
      </section>

      <section id="services" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Gallery Grid */}
          <div className="mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols- gap-6">
              {images.map((image) => (
                <React.Fragment key={image.id}>
                  <div className="bg-gradient-to-r from-gray-100 via-white to-gray-200 cursor-pointer overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-all duration-300 transform ">
                    <div className="aspect-w-4 aspect-h-3 relative">
                      <Image
                        src={image.src}
                        alt={image.alt}
                        width={400}
                        height={300}
                        className="w-full md:h-[280px] object-cover transition-transform duration-300"
                      />
                    </div>
                    <div className="p-3">
                      <div className="text-center text-xl font-semibold text-gray-900 mb-3">
                        {image.title}
                      </div>
                      <div className="text-center">
                        <a
                          href="/services/page-details"
                          className=" mb-3 text-blue-600 hover:underline"
                        >
                          Learn More →
                        </a>
                      </div>
                    </div>
                  </div>
                </React.Fragment>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
