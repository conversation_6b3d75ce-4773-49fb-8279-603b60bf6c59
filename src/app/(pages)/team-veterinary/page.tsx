"use client";

import { Calendar } from "lucide-react";
import Image from "next/image";

export default function TeamVeterinary() {
  const services = [
    {
      title: "<PERSON>",
      subtitle: "Vet Assistant",
      description:
        "<PERSON> has been working as a veterinary assistant and technician for six years. He is bilingual, patient, and takes pride in being the father of two dogs, two cats, and two snakes. For <PERSON>, the most rewarding part of his job is seeing pets reunited with their owners after recovering from their ailments.",
      image: "../images/<PERSON>-<PERSON>-<PERSON>.webp",
    },
    {
      title: "<PERSON>",
      subtitle: "Receptionist",
      description:
        "<PERSON> is currently pursuing a career in the Veterinary field as a vet tech. She is currently attending college to be licensed as a RVT. She has many years experience with handling dogs and wants to continue learning about other animals. What excites <PERSON> about working here is the hands on experience along with helping make pets feel better.",
      image: "../images/<PERSON>-<PERSON>.webp",
    },
    {
      title: "<PERSON>",
      subtitle: "Vet Assistant",
      description:
        "<PERSON> specializes in making sure that your furry friends and you experience only the best and most affordable veterinary services in the central valley. He ensures that the health and well-being of your pets are our #1 priority here at Dry Creek Veterinary Hospital.",
      image: "../images/coming-soon-placeholder.webp",
    },
  ];
  return (
    <div>
      <section id="about" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
                  Meet{" "}
                  <span className="text-blue-600 block">
                    Our Veterinary Team
                  </span>
                </h2>
                <p className="text-gray-600 max-w-2xl">
                  Meet the veterinarian and team at Dry Creek Veterinary
                  Hospital. We’re pleased to provide exceptional vet care for
                  your pets.
                </p>
                <a href="/contact-us">
                  <button className="bg-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center cursor-pointer">
                    <Calendar className="w-5 h-5 mr-2" />
                    Book Appointment
                  </button>
                </a>
              </div>
            </div>

            <div className="relative flex justify-end">
              <Image
                src="/images/pet-teams.webp"
                alt="Veterinary hospital interior"
                width={600}
                height={400}
                className="rounded-2xl shadow-xl md:h-[380px] md:w-[600px] rounded-tl-[20px] rounded-tr-[20px] rounded-br-[20px] rounded-bl-[50px] md:rounded-bl-[100px]"
              />
            </div>
          </div>
        </div>
      </section>

      <section id="team" className="py-20 bg-blue-600">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-1 gap-12 items-center text-center">
            <div className="space-y-6">
              <h2 className="text-2xl md:text-4xl font-bold text-white">
                Get to Know Our Team
              </h2>
              <p className="text-white max-w-[45rem] m-auto">
                We are solely dedicated to your pet’s care. We look forward to
                getting to know you, and we hope you’ll take the time to scroll
                down and get to know us.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section id="services" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Team
            </h2>
          </div>

          <div className="flex flex-wrap justify-center gap-6 ">
            {services.map((service, index) => (
              <div
                key={index}
                className="bg-gradient-to-r from-gray-100 via-white to-gray-200 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden  w-[360px]"
              >
                <div className="relative overflow-hidden">
                  <Image
                    src={service.image}
                    alt={service.title}
                    width={400}
                    height={192}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                </div>
                <div className="p-6 ">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {service.title}
                  </h3>
                  <h4 className="text-lg mb-3">{service.subtitle}</h4>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {service.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
