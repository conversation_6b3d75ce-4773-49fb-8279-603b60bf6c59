"use client";

import { useState } from "react";
import Image from "next/image";
import { Calendar, ChevronDownIcon } from "lucide-react";

const VeterinaryFAQServices = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const faqData = [
    "What types of payment options are available?",
    "Are there payment plans available for my pet's care?",
    "Will I be provided with an estimate of costs?",
    "Should I call ahead to book an appointment?",
    "What is your appointment cancellation policy?",
    "What types of animals do you treat?",
    "Where can I purchase my pet's prescription and food?",
    "What are the fees associated with a specialty appointment?",
    "Can you help with my pet insurance?",
  ];

  const services = [
    {
      icon: "🏥",
      title: "Comprehensive Care",
      color: "bg-blue-50",
    },
    {
      icon: "🛡️",
      title: "State-of-the-Art Technology",
      color: "bg-blue-50",
    },
    {
      icon: "❤️",
      title: "Compassionate Staff",
      color: "bg-blue-50",
    },
    {
      icon: "🎯",
      title: "Preventative Health Focus",
      color: "bg-blue-50",
    },
    {
      icon: "🏆",
      title: "Accredited Excellence",
      color: "bg-blue-50",
    },
  ];

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <>
      <section id="faqs" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
                  <span className="text-blue-600">FAQs</span>
                </h2>
                <p className=" text-gray-600 max-w-2xl">
                  There is no question too big or too small for our veterinary
                  team. Below are some answers to our most common questions.
                </p>
                <a href="/contact-us">
                  <button className="bg-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center cursor-pointer">
                    <Calendar className="w-5 h-5 mr-2" />
                    Book Appointment
                  </button>
                </a>
              </div>
            </div>

            <div className="relative flex justify-end">
              <Image
                src="/images/faqs.webp"
                alt="Veterinary hospital interior"
                width={600}
                height={400}
                className="rounded-2xl shadow-xl md:h-[380px] md:w-[600px] rounded-tl-[20px] rounded-tr-[20px] rounded-br-[20px] rounded-bl-[50px] md:rounded-bl-[100px]"
              />
            </div>
          </div>
        </div>
      </section>
      <div className="min-h-screen bg-gray-50">
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-4xl font-bold text-center mb-12 text-gray-900 ">
              Frequently Asked Questions
            </h2>

            <div className="space-y-4">
              {faqData.map((question, index) => (
                <div
                  key={index}
                  className="bg-white rounded-lg shadow-sm border border-gray-200"
                >
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset rounded-lg"
                  >
                    <span className="text-blue-600 font-medium text-lg">
                      {question}
                    </span>
                    <ChevronDownIcon
                      className={`w-5 h-5 text-blue-600 transform transition-transform duration-200 ${
                        openFAQ === index ? "rotate-180" : ""
                      }`}
                    />
                  </button>

                  {openFAQ === index && (
                    <div className="px-6 pb-4">
                      <div className="border-t pt-4 text-gray-600">
                        <p>
                          Answer content for {question} would go here. This is
                          where you would provide detailed information to
                          address the customer question.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Veterinary Medical Center Difference Section */}
        <div className="py-16 px-4 sm:px-6 lg:px-8 bg-blue-50">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl md:text-4xl font-bold text-center mb-12 text-blue-600 ">
              Veterinary Medical Center Difference
            </h2>

            <div className="flex flex-col lg:flex-row items-center gap-12">
              {/* Pet Image */}
              <div className="lg:w-1/4 flex justify-center">
                <div className="w-40 h-40 rounded-full overflow-hidden shadow-lg bg-gray-800">
                  <Image
                    src="/images/dog-cat-with-veterinarian-faqs.avif"
                    alt="Dog and Cat"
                    width={160}
                    height={160}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              {/* Services Grid */}
              <div className="lg:w-3/4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                  {services.map((service, index) => (
                    <div
                      key={index}
                      className={`${service.color} bg-gradient-to-r from-gray-100 via-white to-gray-200 cursor-pointer rounded-lg p-6 text-center bg- shadow-sm hover:shadow-md transition-shadow duration-300`}
                    >
                      <div className="text-3xl mb-3">{service.icon}</div>
                      <h3 className="text-blue-600 font-semibold text-sm leading-tight">
                        {service.title}
                      </h3>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default VeterinaryFAQServices;
