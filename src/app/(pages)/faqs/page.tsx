"use client";

import { useState } from "react";
import Image from "next/image";
import { ChevronDownIcon } from "lucide-react";

const VeterinaryFAQServices = () => {
  const [openFAQ, setOpenFAQ] = useState(null);

  const faqData = [
    "What types of payment options are available?",
    "Are there payment plans available for my pet's care?",
    "Will I be provided with an estimate of costs?",
    "Should I call ahead to book an appointment?",
    "What is your appointment cancellation policy?",
    "What types of animals do you treat?",
    "Where can I purchase my pet's prescription and food?",
    "What are the fees associated with a specialty appointment?",
    "Can you help with my pet insurance?",
  ];

  const services = [
    {
      icon: "🏥",
      title: "Comprehensive Care",
      color: "bg-blue-50",
    },
    {
      icon: "🛡️",
      title: "State-of-the-Art Technology",
      color: "bg-blue-50",
    },
    {
      icon: "❤️",
      title: "Compassionate Staff",
      color: "bg-blue-50",
    },
    {
      icon: "🎯",
      title: "Preventative Health Focus",
      color: "bg-blue-50",
    },
    {
      icon: "🏆",
      title: "Accredited Excellence",
      color: "bg-blue-50",
    },
  ];

  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* FAQ Section */}
      <div className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <h2
            className="text-4xl font-bold text-center mb-12 text-blue-600"
            style={{ fontFamily: "cursive" }}
          >
            Frequently Asked Questions
          </h2>

          <div className="space-y-4">
            {faqData.map((question, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-sm border border-gray-200"
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset rounded-lg"
                >
                  <span className="text-blue-600 font-medium text-lg">
                    {question}
                  </span>
                  <ChevronDownIcon
                    className={`w-5 h-5 text-blue-600 transform transition-transform duration-200 ${
                      openFAQ === index ? "rotate-180" : ""
                    }`}
                  />
                </button>

                {openFAQ === index && (
                  <div className="px-6 pb-4">
                    <div className="border-t pt-4 text-gray-600">
                      <p>
                        Answer content for "{question}" would go here. This is
                        where you would provide detailed information to address
                        the customer's question.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Veterinary Medical Center Difference Section */}
      <div className="py-16 px-4 sm:px-6 lg:px-8 bg-blue-50">
        <div className="max-w-6xl mx-auto">
          <h2
            className="text-4xl font-bold text-center mb-16 text-blue-600"
            style={{ fontFamily: "cursive" }}
          >
            Veterinary Medical Center Difference
          </h2>

          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Pet Image */}
            <div className="lg:w-1/4 flex justify-center">
              <div className="w-40 h-40 rounded-full overflow-hidden shadow-lg bg-gray-800">
                <Image
                  src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 160 160'%3E%3Ccircle cx='80' cy='80' r='80' fill='%23374151'/%3E%3Ctext x='80' y='90' text-anchor='middle' fill='white' font-size='20'%3EPet Photo%3C/text%3E%3C/svg%3E"
                  alt="Dog and Cat"
                  width={160}
                  height={160}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Services Grid */}
            <div className="lg:w-3/4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                {services.map((service, index) => (
                  <div
                    key={index}
                    className={`${service.color} rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow duration-300`}
                  >
                    <div className="text-3xl mb-3">{service.icon}</div>
                    <h3 className="text-blue-600 font-semibold text-sm leading-tight">
                      {service.title}
                    </h3>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VeterinaryFAQServices;
