"use client";
import React from "react";
import {
  ExternalLink,
  Heart,
  Shield,
  Stethoscope,
  ArrowRight,
  Calendar,
} from "lucide-react";
import Image from "next/image";

const HelpfulLinksSection = () => {
  const links = [
    {
      id: 1,
      title: "Veterinary Information Network",
      description:
        "Professional resources, continuing education, and peer support for veterinary professionals worldwide.",
      icon: <Stethoscope className="w-8 h-8" />,
      color: "bg-blue-50 border-blue-200 hover:bg-blue-100",
      iconColor: "text-blue-600",
      buttonColor: "bg-blue-600 hover:bg-blue-700",
      url: "https://www.vin.com/vin/",
      features: ["CE Credits", "Peer Forums", "Case Studies"],
    },
    {
      id: 2,
      title: "Companion Animal Parasite Council",
      description:
        "Evidence-based guidelines for parasite prevention and treatment in companion animals.",
      icon: <Shield className="w-8 h-8" />,
      color: "bg-green-50 border-green-200 hover:bg-green-100",
      iconColor: "text-green-600",
      buttonColor: "bg-green-600 hover:bg-green-700",
      url: "https://www.petsandparasites.org/",
      features: ["Treatment Guidelines", "Prevention Tips", "Research Updates"],
    },
    {
      id: 3,
      title: "Veterinary Oral Health Council",
      description:
        "Promoting oral health in animals through education, certification, and research initiatives.",
      icon: <Heart className="w-8 h-8" />,
      color: "bg-purple-50 border-purple-200 hover:bg-purple-100",
      iconColor: "text-purple-600",
      buttonColor: "bg-purple-600 hover:bg-purple-700",
      url: "https://vohc.org/",
      features: ["Dental Standards", "Product Reviews", "Training Resources"],
    },
  ];

  const handleLinkClick = (url: string) => {
    window.open(url, "_blank");
  };

  return (
    <>
      <section id="about" className="py-20 bg-gray-50">
        <div className="container mx-auto relative overflow-hidden px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
                  Helpful <span className="text-blue-600">Links</span>
                </h2>
                <p className="text-gray-600 max-w-2xl">
                  Discover a wealth of resources designed to assist you in
                  caring for your pets.
                </p>
                <a href="/contact-us">
                  <button className="bg-blue-600 text-white px-8 py-4 mt-8 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center cursor-pointer">
                    <Calendar className="w-5 h-5 mr-2" />
                    Book Appointment
                  </button>
                </a>
              </div>
            </div>

            <div className="relative flex md:justify-end">
              <Image
                src="/images/link.jpg"
                alt="Veterinary hospital interior"
                width={500}
                height={400}
                className="rounded-2xl z-10 shadow-xl  rounded-tl-[20px] rounded-tr-[20px] rounded-br-[20px] rounded-bl-[50px] md:rounded-bl-[100px]"
              />
            </div>
          </div>
          <div className="absolute top-10 left-10 w-16 h-16 bg-blue-200/30 rounded-full "></div>
          <div className="absolute bottom-20 left-[55rem] w-32 h-32 bg-blue-300/30 rounded-full"></div>
        </div>
      </section>

      <section className="pb-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Our Helpful
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 ml-2">
                Links
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Access trusted veterinary resources and professional organizations
              to enhance your practice and knowledge.
            </p>
          </div>

          {/* Links Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {links.map((link) => (
              <div
                key={link.id}
                className={`${link.color} border-2 rounded-2xl p-6 transition-all duration-300 hover:shadow-lg hover:scale-105 group cursor-pointer`}
                onClick={() => handleLinkClick(link.url)}
              >
                {/* Icon and Title */}
                <div className="flex items-start mb-4">
                  <div
                    className={`${link.iconColor} p-3 bg-white rounded-xl shadow-sm group-hover:scale-110 transition-transform duration-200`}
                  >
                    {link.icon}
                  </div>
                  <ExternalLink className="w-4 h-4 text-gray-400 ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                </div>

                {/* Content */}
                <div className="mb-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors">
                    {link.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed mb-4">
                    {link.description}
                  </p>
                </div>

                {/* Button */}
                <button
                  className={`${link.buttonColor} cursor-pointer text-white px-6 py-3 rounded-xl font-semibold text-sm w-full transition-all duration-200 flex items-center justify-center gap-2 group-hover:gap-3 shadow-sm hover:shadow-md`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLinkClick(link.url);
                  }}
                >
                  Learn More
                  <ArrowRight className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1" />
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default HelpfulLinksSection;
