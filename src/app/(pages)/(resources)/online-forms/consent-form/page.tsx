"use client";
import CaptchaInput from "@/app/components/form/captcha-input";
import SignatureInput from "@/app/components/form/signature";
import TextInput from "@/app/components/form/text-input";
import {
  emailValidationRule,
  mobileValidationRule,
} from "@/app/constant/constant";
import Image from "next/image";
import Link from "next/link";
import { useRef } from "react";
import { useForm } from "react-hook-form";

interface NewClientFormTypes {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  signature: string;
  date: string;
  petName: string;
  captcha: string;
}

const ConsentForm = () => {
  const newClientFormRef = useRef<HTMLDivElement>(null);

  const { control, handleSubmit } = useForm<NewClientFormTypes>({
    defaultValues: { email: "" },
  });

  const handleScrollToForm = () => {
    if (newClientFormRef.current) {
      const navbarHeight = 100;
      const elementPosition =
        newClientFormRef.current.getBoundingClientRect().top +
        window.pageYOffset;
      const offsetPosition = elementPosition - navbarHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    }
  };

  const onSubmit = (data: NewClientFormTypes) => {
    console.log(data);
  };

  return (
    <div>
      <section id="about" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
                  Consent <span className="text-blue-600">Form</span>
                </h2>
                <p>
                  Save time at your pet’s upcoming appointment by completing our
                  online consent form.
                </p>
                <button
                  onClick={handleScrollToForm}
                  className="bg-blue-600 cursor-pointer text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
                >
                  Get Started
                </button>
              </div>
            </div>

            <div className="relative">
              <Image
                src="/images/pet-teams.webp"
                alt="Veterinary hospital interior"
                width={600}
                height={400}
                className="rounded-2xl shadow-xl md:h-[380px] md:w-[600px] rounded-tl-[20px] rounded-tr-[20px] rounded-br-[20px] rounded-bl-[50px] md:rounded-bl-[100px]"
              />
            </div>
          </div>
        </div>
      </section>

      <section id="team" className="py-20 bg-blue-600">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-1 gap-12 items-center text-center">
            <div className="space-y-6">
              <h2 className="text-2xl md:text-4xl font-bold text-white">
                Consent Form
              </h2>
              <p className="text-white max-w-[45rem] m-auto">
                Please fill out this form as completely and accurately as
                possible so we can get to know you and your pet(s) before your
                visit.
              </p>
            </div>
          </div>
        </div>
      </section>
      <section
        ref={newClientFormRef}
        className="mt-12 border border-gray-300 p-4 my-8 rounded-md scroll-smooth py-10 max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-6"
      >
        {/* Name */}
        <TextInput
          control={control}
          name="firstName"
          label="First Name"
          placeholder="First Name"
          required
        />
        <TextInput
          control={control}
          name="lastName"
          label="Last Name"
          placeholder="Last Name"
          required
        />
        <TextInput
          control={control}
          name="phone"
          label="Phone"
          placeholder="Phone"
          type="tel"
          maxLength={10}
          required
          rules={mobileValidationRule}
        />

        <TextInput
          control={control}
          name="email"
          label="Email"
          placeholder="Email"
          type="email"
          required
          rules={emailValidationRule}
        />

        <TextInput
          control={control}
          name="petName"
          label="Pet's Name"
          placeholder="Pet's Name"
          required
        />
        <div className="col-span-2">
          <p className="font-serif text-black mb-6">Dear Client,</p>

          <p className="font-serif text-black mb-6">
            If you have any questions, please feel free to call or email the
            hospital at{" "}
            <Link
              href={`mailto:<EMAIL>`}
              className="text-blue-800 font-semibold"
            >
              (************* / <EMAIL>
            </Link>{" "}
            . On your pets surgery day, you will be required to review and sign
            an Authorization/Estimate Form. We will also double-check to ensure
            we have a phone number where you can be reached during your pets
            surgery time.
          </p>

          <p className="font-serif text-black mb-6">
            The night before your pet’s surgery, we ask that you refrain from
            providing any food or treats after 10:00 pm. Water is fine and may
            be left out for your pet to consume. If you are currently
            administering any medications, vitamins, and/or injections, withhold
            the morning doses unless otherwise instructed by the vet.
          </p>

          <p className="font-serif text-black mb-6">
            At the time of your pets scheduled surgery, the vet will address any
            remaining questions or concerns you may have and will collect the
            Authorization Form that you have completed.
          </p>

          <p className="font-serif text-black mb-6">
            If you have elected to have any of the recommended blood tests
            performed, these will be collected and done prior to the start of
            surgery, if not already performed in advance. If any questions
            arise, the vet may contact you at the number on the Authorization
            Form.
          </p>

          <p className="font-serif text-black mb-6">
            Upon the completion of the surgery, the vet will process your
            payment, go over all discharge orders verbally, and provide you with
            a written copy. If you do not understand any instructions, please do
            not hesitate to ask for clarification prior to or after our
            departure.
          </p>

          <p className="font-serif text-black mb-6">
            We know surgery days can induce stress and anxiety.
            <Link
              href="https://drycreekvet.com/about-dry-creek-veterinary-hospital/"
              target="_blank"
              className="text-blue-800 font-semibold"
            >
              {" "}
              Dry Creek Veterinary Hospital
            </Link>{" "}
            appreciates the trust you place in us, and we want to make the
            experience as pleasant as possible for both you and your pet.
          </p>

          <p className="font-serif text-black">
            We look forward to serving you and your pet on the upcoming surgery
            day.
          </p>
        </div>

        <div className="col-span-2">
          <SignatureInput
            control={control}
            name="signature"
            label="Signature"
            required
          />
        </div>
        <TextInput
          control={control}
          name="date"
          label="Date"
          type="date"
          required
        />
        {/* ✅ Captcha here */}
        <CaptchaInput
          control={control}
          name="captcha"
          label="Verify you are human"
          className="col-span-2 mt-3"
        />
        <div className="col-span-2 mt-3">
          <button
            onClick={handleSubmit(onSubmit)}
            className=" bg-blue-600 cursor-pointer text-white px-6 py-2 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            Submit
          </button>
        </div>
      </section>
    </div>
  );
};
export default ConsentForm;
